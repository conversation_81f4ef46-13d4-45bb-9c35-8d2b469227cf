# 苹果登录功能实现说明

## 功能概述
已为树小柒App完成苹果登录功能的实现，包括UI交互、API接口预留和令牌获取。

## 实现内容

### 1. 框架导入
- 在 `LoginViewController.swift` 和 `LoginViewController2.0.swift` 中导入了 `AuthenticationServices` 框架
- 支持iOS 13.0及以上版本的苹果登录功能

### 2. UI交互
- 为两个登录控制器的苹果登录按钮添加了点击事件
- 为主界面和底部弹窗的苹果登录按钮添加了点击事件
- 实现了苹果登录的触发逻辑

### 3. 苹果登录流程
- 创建苹果登录请求，请求用户的姓名和邮箱权限
- 在两个登录控制器中都实现了 `ASAuthorizationControllerDelegate` 和 `ASAuthorizationControllerPresentationContextProviding` 代理
- 处理登录成功和失败的回调
- 统一的错误处理和用户体验

### 4. API接口预留
- 在 `APIRouter.swift` 中添加了苹果登录的API路由：`/api/video/user/appleLoginOneTouch.do`
- 在 `APIManager.swift` 中添加了 `loginWithApple` 方法
- 在 `LoginViewModel.swift` 中添加了苹果登录的业务逻辑
- 使用与微信登录相同的返回参数逻辑

### 5. 权限配置
- 在 `Shuxiaoqi.entitlements` 中添加了苹果登录权限配置

## 获取的令牌信息

当用户完成苹果登录时，会获取以下信息：
- **User ID**: 苹果提供的唯一用户标识符
- **Full Name**: 用户姓名（首次登录时提供）
- **Email**: 用户邮箱（首次登录时提供）
- **Authorization Code**: 授权码
- **Identity Token**: 身份令牌（JWT格式，包含用户信息）

所有信息都会在控制台打印，方便调试和查看。

## 测试方法

1. 在真机上运行App（苹果登录不支持模拟器）
2. 进入登录页面
3. 点击苹果登录按钮
4. 完成苹果ID验证
5. 查看控制台输出的令牌信息
6. 验证API请求参数格式是否正确

### 预期的API请求格式
```json
{
    "accessToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...", // 苹果identityToken
    "appType": "ys_video",
    "inviteCode": 0  // 可选，不传时不包含此字段
}
```

## 后端对接

API接口已按照您提供的规范配置完成：
- **接口路径**: `/api/video/user/appleLoginOneTouch.do`
- **请求方法**: POST
- **参数格式**:
  - `accessToken`: 苹果登录的identityToken
  - `appType`: 默认"ys_video"
  - `inviteCode`: 邀请码（可选，不传）

### 当前API配置
```swift
// APIRouter.swift
static func appleLogin(accessToken: String, inviteCode: Int? = nil) -> APIRequest {
    var parameters: [String: Any] = [
        "accessToken": accessToken,
        "appType": "ys_video"
    ]

    if let inviteCode = inviteCode {
        parameters["inviteCode"] = inviteCode
    }

    return APIRequest(
        path: "/api/video/user/appleLoginOneTouch.do",
        method: .post,
        parameters: parameters
    )
}
```

### 调用示例
```swift
// LoginViewModel.swift
func loginWithApple(accessToken: String, inviteCode: Int? = nil) {
    APIManager.shared.loginWithApple(accessToken: accessToken, inviteCode: inviteCode) { result in
        switch result {
        case .success(let response):
            // 处理登录成功，使用与微信登录相同的逻辑
        case .failure(let error):
            // 处理登录失败
        }
    }
}
```

### 参数说明
- **accessToken**: 将苹果登录获取的identityToken作为accessToken传递
- **appType**: 固定为"ys_video"
- **inviteCode**: 邀请码，可选参数，默认不传
- **返回参数**: 与微信登录共用一套逻辑

## 注意事项

1. 苹果登录需要在苹果开发者中心配置Sign in with Apple功能
2. 需要真机测试，模拟器不支持苹果登录
3. 用户首次登录时会提供姓名和邮箱，后续登录可能不会再次提供
4. Identity Token是JWT格式，包含了用户的基本信息和签名

## 错误处理

实现了完整的错误处理机制：
- 用户取消登录：不显示错误提示
- 登录失败：显示相应的错误信息
- 网络错误：显示网络相关错误信息
- 系统版本不支持：提示需要iOS 13.0及以上版本
