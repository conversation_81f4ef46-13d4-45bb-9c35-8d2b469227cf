//
//  CommentCellTest.swift
//  Shuxiaoqi
//
//  Created by AI Assistant on 2025/7/18.
//  Test file to verify CommentCell author label text display fix
//

import UIKit
import SnapKit

class CommentCellTestViewController: UIViewController {
    
    private lazy var tableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.delegate = self
        table.dataSource = self
        table.backgroundColor = UIColor(hex: "#F5F5F5")
        table.separatorStyle = .none
        table.register(CommentCell.self, forCellReuseIdentifier: "CommentCell")
        table.register(CommentReplyCell.self, forCellReuseIdentifier: "CommentReplyCell")
        table.estimatedRowHeight = 140
        table.rowHeight = UITableView.automaticDimension
        return table
    }()
    
    // Test data with author comments
    private var testComments: [CommentModel] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "CommentCell Author Label Test"
        view.backgroundColor = UIColor(hex: "#F5F5F5")

        setupTestData()
        setupUI()

        // 测试分类映射功能
        testCategoryMapping()
    }
    
    private func setupTestData() {
        // Create test comments with different scenarios
        testComments = [
            CommentModel(
                id: "1",
                customerId: "user1",
                avatar: "default_avatar",
                username: "普通用户",
                isAuthor: false,
                content: "这是一条普通评论，没有作者标签",
                timestamp: "2小时前",
                likes: 5,
                dislikes: 0,
                isLiked: false,
                isDisliked: false,
                imageUrl: nil,
                replyTo: nil,
                isHasChild: false,
                childCount: 0,
                replies: nil,
                level: 0,
                mentionRanges: [],
                isMine: false
            ),
            CommentModel(
                id: "2",
                customerId: "author1",
                avatar: "default_avatar",
                username: "视频作者",
                isAuthor: true, // This should show the author label with gradient background
                content: "这是作者的评论，应该显示橙色到红色渐变背景的作者标签，白色文字应该清晰可见",
                timestamp: "1小时前",
                likes: 12,
                dislikes: 0,
                isLiked: false,
                isDisliked: false,
                imageUrl: nil,
                replyTo: nil,
                isHasChild: false,
                childCount: 0,
                replies: nil,
                level: 0,
                mentionRanges: [],
                isMine: false
            ),
            CommentModel(
                id: "3",
                customerId: "author2",
                avatar: "default_avatar",
                username: "另一个作者",
                isAuthor: true, // This should also show the author label with gradient background
                content: "另一条作者评论，测试新的渐变背景View+Label方案是否正常工作",
                timestamp: "30分钟前",
                likes: 8,
                dislikes: 0,
                isLiked: true,
                isDisliked: false,
                imageUrl: nil,
                replyTo: nil,
                isHasChild: false,
                childCount: 0,
                replies: nil,
                level: 0,
                mentionRanges: [],
                isMine: false
            ),
            // 添加二级评论测试用例
            CommentModel(
                id: "4",
                customerId: "user2",
                avatar: "default_avatar",
                username: "普通用户回复",
                isAuthor: false,
                content: "这是一条二级评论，普通用户发的",
                timestamp: "15分钟前",
                likes: 3,
                dislikes: 0,
                isLiked: false,
                isDisliked: false,
                imageUrl: nil,
                replyTo: "视频作者",
                isHasChild: false,
                childCount: 0,
                replies: nil,
                level: 1, // 二级评论
                mentionRanges: [],
                isMine: false
            ),
            CommentModel(
                id: "5",
                customerId: "author3",
                avatar: "default_avatar",
                username: "作者回复",
                isAuthor: true, // 作者的二级评论，应该显示作者标签
                content: "这是作者的二级评论回复，应该显示渐变作者标签",
                timestamp: "10分钟前",
                likes: 6,
                dislikes: 0,
                isLiked: false,
                isDisliked: false,
                imageUrl: nil,
                replyTo: "普通用户",
                isHasChild: false,
                childCount: 0,
                replies: nil,
                level: 1, // 二级评论
                mentionRanges: [],
                isMine: false
            )
        ]
    }
    
    private func setupUI() {
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
}

// MARK: - UITableViewDataSource & UITableViewDelegate
extension CommentCellTestViewController: UITableViewDataSource, UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return testComments.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let comment = testComments[indexPath.row]

        // 根据评论层级选择不同的cell
        if comment.level == 0 {
            // 一级评论使用CommentCell
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "CommentCell", for: indexPath) as? CommentCell else {
                return UITableViewCell()
            }

            cell.configure(with: comment)

            // Add some test callbacks
            cell.likeCallback = {
                print("Like tapped for comment: \(comment.username)")
            }

            cell.dislikeCallback = {
                print("Dislike tapped for comment: \(comment.username)")
            }

            cell.avatarTapCallback = {
                print("Avatar tapped for comment: \(comment.username)")
            }

            return cell
        } else {
            // 二级评论使用CommentReplyCell
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "CommentReplyCell", for: indexPath) as? CommentReplyCell else {
                return UITableViewCell()
            }

            cell.configure(with: comment)

            // Add some test callbacks
            cell.likeCallback = {
                print("Reply Like tapped for comment: \(comment.username)")
            }

            cell.dislikeCallback = {
                print("Reply Dislike tapped for comment: \(comment.username)")
            }

            cell.avatarTapCallback = {
                print("Reply Avatar tapped for comment: \(comment.username)")
            }

            return cell
        }
    }

    // MARK: - 测试分类映射功能
    private func testCategoryMapping() {
        print("🧪 开始测试分类映射功能...")

        // 测试用例1：有labels的情况
        var worksInfo1 = WorksInfo()
        worksInfo1.labels = ["舞蹈", "教学"]
        worksInfo1.worksCategoryName = "摄影"

        var commentItem1 = CommentItem()
        commentItem1.worksInfo = worksInfo1
        commentItem1.commentDesc = "测试评论1"
        commentItem1.likeNumber = 5

        let historyModel1 = commentItem1.toHistoryModel()
        print("✅ 测试用例1 - 有labels: \(historyModel1.tags) (应该显示: [\"舞蹈\", \"教学\"])")

        // 测试用例2：没有labels但有分类的情况
        var worksInfo2 = WorksInfo()
        worksInfo2.labels = [] // 空数组
        worksInfo2.worksCategoryName = "美食"

        var commentItem2 = CommentItem()
        commentItem2.worksInfo = worksInfo2
        commentItem2.commentDesc = "测试评论2"
        commentItem2.likeNumber = 3

        let historyModel2 = commentItem2.toHistoryModel()
        print("✅ 测试用例2 - 无labels有分类: \(historyModel2.tags) (应该显示: [\"美食\"])")

        // 测试用例3：既没有labels也没有分类的情况
        var worksInfo3 = WorksInfo()
        worksInfo3.labels = nil
        worksInfo3.worksCategoryName = nil

        var commentItem3 = CommentItem()
        commentItem3.worksInfo = worksInfo3
        commentItem3.commentDesc = "测试评论3"
        commentItem3.likeNumber = 0

        let historyModel3 = commentItem3.toHistoryModel()
        print("✅ 测试用例3 - 无labels无分类: \(historyModel3.tags) (应该显示: [])")

        // 测试用例4：分类为空字符串的情况
        var worksInfo4 = WorksInfo()
        worksInfo4.labels = []
        worksInfo4.worksCategoryName = ""

        var commentItem4 = CommentItem()
        commentItem4.worksInfo = worksInfo4
        commentItem4.commentDesc = "测试评论4"
        commentItem4.likeNumber = 1

        let historyModel4 = commentItem4.toHistoryModel()
        print("✅ 测试用例4 - 分类为空字符串: \(historyModel4.tags) (应该显示: [])")

        print("🎉 分类映射功能测试完成！")
    }
}
 