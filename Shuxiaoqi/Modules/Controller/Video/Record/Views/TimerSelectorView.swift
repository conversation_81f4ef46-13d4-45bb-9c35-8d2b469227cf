import UIKit

/// 定时选项
enum TimerOption: Int, CaseIterable {
    case off = 0
    case sec3 = 3
    case sec5 = 5
    case sec10 = 10
    
    var title: String {
        switch self {
        case .off: return "关"
        case .sec3: return "3秒"
        case .sec5: return "5秒"
        case .sec10: return "10秒"
        }
    }
}

/// 定时选择器弹窗（横向排列，支持自定义位置）
class TimerSelectorView: UIView {
    // 回调：选中定时
    var onSelect: ((TimerOption) -> Void)?
    var onSelectedTitleChanged: ((String) -> Void)?
    private var selectedOption: TimerOption?
    private var buttonList: [UIButton] = []
    private var separatorViews: [UIView] = []
    private let contentView = UIView()
    private let titleIcon = UIImageView(image: UIImage(named: "video_setting_timing"))
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.text = "定时"
        label.textColor = .white
        label.font = .systemFont(ofSize: 15, weight: .medium)
        label.textAlignment = .left
        return label
    }()
    private let topSeparator = UIView()
    private let buttonStack = UIStackView()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setSelected(.off) // 默认选中关
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setSelected(.off) // 默认选中关
    }
    
    private func setupUI() {
        backgroundColor = .clear
        addSubview(contentView)
        contentView.backgroundColor = .clear
        contentView.layer.cornerRadius = 8
        contentView.layer.masksToBounds = true
        
        // 毛玻璃
        let blurEffect = UIBlurEffect(style: .systemMaterialDark)
        let blurView = UIVisualEffectView(effect: blurEffect)
        blurView.layer.cornerRadius = 8
        blurView.clipsToBounds = true
        blurView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(blurView)
        NSLayoutConstraint.activate([
            blurView.topAnchor.constraint(equalTo: contentView.topAnchor),
            blurView.leftAnchor.constraint(equalTo: contentView.leftAnchor),
            blurView.rightAnchor.constraint(equalTo: contentView.rightAnchor),
            blurView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor)
        ])
        // 半透明色块
        let colorView = UIView()
        colorView.backgroundColor = UIColor(hex: "#43413E").withAlphaComponent(0.2)
        colorView.layer.cornerRadius = 8
        colorView.clipsToBounds = true
        colorView.isUserInteractionEnabled = false
        colorView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(colorView)
        NSLayoutConstraint.activate([
            colorView.topAnchor.constraint(equalTo: contentView.topAnchor),
            colorView.leftAnchor.constraint(equalTo: contentView.leftAnchor),
            colorView.rightAnchor.constraint(equalTo: contentView.rightAnchor),
            colorView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor)
        ])
        // 内容直接加到contentView上
        contentView.addSubview(titleIcon)
        contentView.addSubview(titleLabel)
        contentView.addSubview(topSeparator)
        contentView.addSubview(buttonStack)
        // 约束全部基于contentView
        contentView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            contentView.widthAnchor.constraint(equalToConstant: 200),
            contentView.heightAnchor.constraint(equalToConstant: 85),
            contentView.topAnchor.constraint(equalTo: topAnchor),
            contentView.rightAnchor.constraint(equalTo: rightAnchor)
        ])
        // 顶部icon+标题
        titleIcon.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            titleIcon.leftAnchor.constraint(equalTo: contentView.leftAnchor, constant: 16),
            titleIcon.centerYAnchor.constraint(equalTo: contentView.topAnchor, constant: 21),
            titleIcon.widthAnchor.constraint(equalToConstant: 20),
            titleIcon.heightAnchor.constraint(equalToConstant: 20),
            titleLabel.leftAnchor.constraint(equalTo: titleIcon.rightAnchor, constant: 8),
            titleLabel.centerYAnchor.constraint(equalTo: titleIcon.centerYAnchor),
            titleLabel.rightAnchor.constraint(equalTo: contentView.rightAnchor, constant: -8),
            titleLabel.heightAnchor.constraint(equalToConstant: 20)
        ])
        // 顶部分割线
        topSeparator.backgroundColor = UIColor.white.withAlphaComponent(0.1)
        topSeparator.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            topSeparator.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 42),
            topSeparator.leftAnchor.constraint(equalTo: contentView.leftAnchor),
            topSeparator.rightAnchor.constraint(equalTo: contentView.rightAnchor),
            topSeparator.heightAnchor.constraint(equalToConstant: 1)
        ])
        // 按钮区
        buttonStack.axis = .horizontal
        buttonStack.alignment = .center
        buttonStack.distribution = .fillEqually
        buttonStack.spacing = 0
        buttonStack.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            buttonStack.topAnchor.constraint(equalTo: topSeparator.bottomAnchor),
            buttonStack.leftAnchor.constraint(equalTo: contentView.leftAnchor, constant: 0),
            buttonStack.rightAnchor.constraint(equalTo: contentView.rightAnchor, constant: 0),
            buttonStack.heightAnchor.constraint(equalToConstant: 42)
        ])
        // 构建按钮和分割线
        let options = TimerOption.allCases
        for (idx, option) in options.enumerated() {
            let btn = UIButton(type: .custom)
            btn.setTitle(option.title, for: .normal)
            btn.setTitleColor(.white, for: .normal)
            btn.setTitleColor(UIColor(hex: "#444444"), for: .selected)
            btn.titleLabel?.font = .systemFont(ofSize: 13)
            btn.backgroundColor = .clear
            btn.layer.cornerRadius = 4
            btn.layer.masksToBounds = true
            btn.tag = option.rawValue
            btn.addTarget(self, action: #selector(optionTapped(_:)), for: .touchUpInside)
            btn.translatesAutoresizingMaskIntoConstraints = false
            buttonStack.addArrangedSubview(btn)
            buttonList.append(btn)
            // 分割线：只加到按钮内部，最后一个不加
            if idx < options.count - 1 {
                let sep = UIView()
                sep.backgroundColor = UIColor.white.withAlphaComponent(0.1)
                sep.translatesAutoresizingMaskIntoConstraints = false
                btn.addSubview(sep)
                NSLayoutConstraint.activate([
                    sep.widthAnchor.constraint(equalToConstant: 1),
                    sep.topAnchor.constraint(equalTo: btn.topAnchor),
                    sep.bottomAnchor.constraint(equalTo: btn.bottomAnchor),
                    sep.trailingAnchor.constraint(equalTo: btn.trailingAnchor)
                ])
            }
        }
    }
    
    func setSelected(_ option: TimerOption) {
        selectedOption = option
        let options = TimerOption.allCases
        for (i, btn) in buttonList.enumerated() {
            let opt = options[i]
            if opt == option {
                btn.backgroundColor = .white
                btn.setTitleColor(UIColor(hex: "#444444"), for: .normal)
                btn.isSelected = true
            } else {
                btn.backgroundColor = .clear
                btn.setTitleColor(.white, for: .normal)
                btn.isSelected = false
            }
        }
        onSelectedTitleChanged?(option.title)
    }
    
    @objc private func optionTapped(_ sender: UIButton) {
        let options = TimerOption.allCases
        guard let option = options.first(where: { $0.rawValue == sender.tag }) else { return }
        setSelected(option)
        // 立即触发onSelect回调
        onSelect?(option)
    }
    
    // 新增：暴露当前选中项属性
    var currentSelectedOption: TimerOption? {
        return selectedOption
    }
    
    // MARK: - 通过title设置选中项
    func setSelectedByTitle(_ title: String) {
        if let option = TimerOption.allCases.first(where: { $0.title == title }) {
            setSelected(option)
        }
    }
}
