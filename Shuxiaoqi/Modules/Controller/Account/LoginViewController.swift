//
//  LoginViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/3/27.
//

import UIKit
import SnapKit
import Combine
import AuthenticationServices

class LoginViewController: BaseViewController {
    
    // MARK: - 新增 UI 组件 (ScrollView & 容器视图)
    private lazy var formScrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.isPagingEnabled = true
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.isScrollEnabled = false // 禁止手动滑动
        scrollView.bounces = false
        return scrollView
    }()
    
    // 短信验证码登录容器
    private lazy var smsLoginContainerView: UIView = UIView()
    // 密码登录容器
    private lazy var passwordLoginContainerView: UIView = UIView()
    
    // 当前登录模式
    private var currentLoginMode: LoginMode = .smsCode

    // MARK: - 为密码登录模式创建新的 UI 元素实例
    private lazy var phoneTextField_pwd: UITextField = createPhoneTextField()
    private lazy var passwordTextField: UITextField = {
        let textField = createBaseTextField(placeholder: "请输入密码")
        textField.isSecureTextEntry = true
        
        // 修改为newPassword类型以获得更好的中文自动填充提示
        if #available(iOS 12.0, *) {
            textField.textContentType = .newPassword
            textField.passwordRules = UITextInputPasswordRules(descriptor: "required: lower; required: upper; required: digit; required: special; minlength: 8;")
        } else {
            textField.textContentType = .password
        }
        
        return textField
    }()
    private lazy var forgotPasswordButton_pwd: UIButton = createForgotPasswordButton()
    private lazy var smsCodeLoginButton: UIButton = createSwitchButton(title: "验证码登录", action: #selector(switchLoginModeTapped))
    
    // MARK: - 短信登录模式 UI 元素 (使用原始 lazy var 初始化)
    private lazy var phoneTextField_sms: UITextField = {
        let textField = createPhoneTextField()
        return textField
    }()
    private lazy var captchaTextField_sms: UITextField = {
        let textField = createBaseTextField(placeholder: "请输入图形验证码")
        // 禁用内置的清除按钮
        textField.clearButtonMode = .never
        
        // 创建右侧视图控制输入区域宽度
        let rightViewContainer = UIView(frame: CGRect(x: 0, y: 0, width: 110, height: DesignSpec.inputFieldHeight))
        textField.rightView = rightViewContainer
        textField.rightViewMode = .always // 始终显示，保持输入区域固定宽度
        
        return textField
    }()
    private lazy var captchaImageView_sms: UIImageView = {
        let imageView = UIImageView()
        imageView.backgroundColor = UIColor(hex: "#F5F5F5")
        imageView.isUserInteractionEnabled = true
        imageView.contentMode = .scaleAspectFill
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(refreshCaptchaTapped))
        imageView.addGestureRecognizer(tapGesture)
        return imageView
    }()
    private lazy var codeTextField_sms: UITextField = {
        let textField = createBaseTextField(placeholder: "请输入验证码")
        textField.keyboardType = .numberPad
        textField.delegate = self
        // 禁用内置的清除按钮，因为我们使用rightView来控制输入区域大小
        textField.clearButtonMode = .never
        
        // 设置为验证码类型以获得正确的中文自动填充提示
        if #available(iOS 12.0, *) {
            textField.textContentType = .oneTimeCode
        }
        
        // 显式指定语言
        textField.tag = 9001 // 使用tag标记验证码输入框
        
        // 创建一个自定义的包含清除按钮的右侧视图
        let rightViewContainer = UIView(frame: CGRect(x: 0, y: 0, width: 120, height: DesignSpec.inputFieldHeight))
        
        // 添加清除按钮
        let clearButton = UIButton(type: .custom)
        clearButton.setImage(UIImage(systemName: "xmark.circle.fill"), for: .normal)
        clearButton.tintColor = UIColor.gray.withAlphaComponent(0.5)
        clearButton.frame = CGRect(x: 0, y: 0, width: 30, height: 30)
        clearButton.center = CGPoint(x: 30, y: rightViewContainer.frame.height/2)
        clearButton.addTarget(self, action: #selector(clearCodeTextField), for: .touchUpInside)
        
        rightViewContainer.addSubview(clearButton)
        textField.rightView = rightViewContainer
        textField.rightViewMode = .whileEditing // 只在编辑时显示
        
        // 添加编辑事件监听，控制清除按钮的显示隐藏
        textField.addTarget(self, action: #selector(codeTextFieldDidChange), for: .editingChanged)
        
        return textField
    }()
    private lazy var getCodeButton_sms: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("获取验证码", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.setTitleColor(UIColor(hex: "#999999"), for: .disabled)
        button.backgroundColor = UIColor(hex: "#FFA245")
        button.layer.cornerRadius = 18
        button.clipsToBounds = true
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        button.addTarget(self, action: #selector(getCodeButtonTapped), for: .touchUpInside)
        return button
    }()
    private lazy var forgotPasswordButton_sms: UIButton = {
        let button = createForgotPasswordButton()
        return button
    }()
    private lazy var passwordLoginButton_sms: UIButton = {
         let button = createSwitchButton(title: "密码登录", action: #selector(switchLoginModeTapped))
         return button
    }()
    
    // MARK: - 不变的 UI 组件
    // (暂不登录, Logo, 标题图片, 主登录按钮, 社交登录相关, 协议相关, 背景, 其他属性)
    
    // 暂不登录按钮
    private lazy var skipButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("暂不登录", for: .normal)
        button.setTitleColor(UIColor(hex: "#000000",alpha: 0.65), for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14)
        button.addTarget(self, action: #selector(skipButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // Logo 图片视图
    private lazy var logoImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "login_logo")
        imageView.contentMode = .scaleAspectFit
        imageView.layer.cornerRadius = 8
        imageView.clipsToBounds = true
        imageView.backgroundColor = UIColor(hex: "#FF5C5C").withAlphaComponent(0.1)
        return imageView
    }()
    
    // 顶部标题图片
    private lazy var titleTopImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "login_title_top")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // 底部标题图片
    private lazy var titleBottomImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "login_title_bm")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // 主登录按钮
    private lazy var loginButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("登录", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = UIColor(hex: "#FF8F1F")
        button.layer.cornerRadius = 25
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        button.layer.shadowColor = UIColor(hex: "#FF5C5C").withAlphaComponent(0.3).cgColor
        button.layer.shadowOffset = CGSize(width: 0, height: 3)
        button.layer.shadowRadius = 6
        button.layer.shadowOpacity = 0.8
        button.addTarget(self, action: #selector(loginButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 其他登录方式 StackView
    private lazy var socialLoginStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .equalSpacing
        stackView.alignment = .center
        stackView.spacing = 30
        return stackView
    }()
    
    // 微信按钮
    private lazy var wechatButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "icon_wechat_login"), for: .normal)
        button.addTarget(self, action: #selector(wechatLoginButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 苹果按钮
    private lazy var appleButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "icon_apple_login"), for: .normal)
        button.addTarget(self, action: #selector(appleLoginButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 分割线
    private lazy var leftLine: UIView = { let v = UIView(); v.backgroundColor = UIColor(hex: "#EEEEEE"); return v }()
    private lazy var rightLine: UIView = { let v = UIView(); v.backgroundColor = UIColor(hex: "#EEEEEE"); return v }()
    
    // 协议选择按钮
    private lazy var agreementCheckButton: UIButton = {
        let button = UIButton(type: .custom)
        // 明确设置图片渲染模式为 .alwaysOriginal
        let normalImage = UIImage(named: "app_radio_Default")?.withRenderingMode(.alwaysOriginal)
        let selectedImage = UIImage(named: "app_radio_select")?.withRenderingMode(.alwaysOriginal)
        button.setImage(normalImage, for: .normal)
        button.setImage(selectedImage, for: .selected)
        button.addTarget(self, action: #selector(agreementCheckTapped), for: .touchUpInside)
        
        return button
    }()

    // 协议标签
    private lazy var agreementLabel: UILabel = {
        let label = UILabel()
        let text = "登录即表示同意《用户协议》和《隐私协议》"
        let attributedString = NSMutableAttributedString(string: text)
        let baseAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 12),
            .foregroundColor: UIColor(hex: "#000000", alpha: 0.45)
        ]
        attributedString.addAttributes(baseAttributes, range: NSRange(location: 0, length: text.count))
        let userAgreementRange = (text as NSString).range(of: "《用户协议》")
        let privacyRange = (text as NSString).range(of: "《隐私协议》")
        let linkAttributes: [NSAttributedString.Key: Any] = [
            .foregroundColor: UIColor(hex: "#0256FF"),
            .font: UIFont.systemFont(ofSize: 12)
        ]
        attributedString.addAttributes(linkAttributes, range: userAgreementRange)
        attributedString.addAttributes(linkAttributes, range: privacyRange)
        label.attributedText = attributedString
        label.isUserInteractionEnabled = true
        let tap = UITapGestureRecognizer(target: self, action: #selector(agreementLabelTapped(_:)))
        label.addGestureRecognizer(tap)
        return label
    }()
    
    // 其他登录方式标签
    private lazy var otherLoginLabel: UILabel = {
        let label = UILabel()
        label.text = "其他登录方式"
        label.textColor = UIColor(hex: "#000000", alpha: 0.45)
        label.font = UIFont.systemFont(ofSize: 14)
        label.textAlignment = .center
        return label
    }()
    
    // 头部容器视图
    private lazy var headerContainerView: UIView = UIView()
    
    // 背景渐变层
    private var gradientLayer: CAGradientLayer?
    // 验证码 Key
    private var captchaKey: String?
    // 验证码ID
    private var codeId: String = ""
    // 倒计时相关变量
    private var countdownDisplayLink: CADisplayLink?
    private var countdownEndTime: Date?
    private var countdownTotalSeconds = 60
    
    // MARK: - 设计规范与常量
    private struct DesignSpec {
        // Header 相关
        static let totalHeight: CGFloat = 174
        static let logoSize: CGFloat = 100
        static let topSpacing: CGFloat = 24
        // static let bottomSpacing: CGFloat = 50 // 不再直接用于 Header
        static let topTitleWidth: CGFloat = 89
        static let topTitleHeight: CGFloat = 23
        static let bottomTitleWidth: CGFloat = 143
        static let bottomTitleHeight: CGFloat = 24
        static let logoToTitleSpacing: CGFloat = 16
        static let titleVerticalSpacing: CGFloat = 16
        static let logoToBottomTitleOffset: CGFloat = 80
        static let topTitleTopOffset: CGFloat = 23
        
        // 表单容器固定高度
        static let formContainerHeight: CGFloat = 260
        
        // 表单内部元素尺寸与间距
        static let inputFieldHeight: CGFloat = 60
        static let inputFieldSpacing: CGFloat = 20
        static let smallButtonHeight: CGFloat = 18
        static let switchButtonTopOffset: CGFloat = 15
        static let horizontalPadding: CGFloat = 30
        
        // 动态按钮间距相关 (用于 formScrollView 底部)
        static let forgotToLoginSpacing: CGFloat = 70 // 基础间距
        static let maxForgotToLoginSpacing: CGFloat = 100
        static let minimumForgotToLoginSpacing: CGFloat = 30
        static let spacingReductionRatio: CGFloat = 0.6
        static let spacingExpansionRatio: CGFloat = 0.5
        static let standardScreenHeight: CGFloat = 667
        static let largeScreenHeight: CGFloat = 812
        
        // 底部元素间距 (保持不变)
        static let loginButtonBottomOffset: CGFloat = -51
        static let agreementTopOffset: CGFloat = 16
        static let otherLoginBottomOffset: CGFloat = -3
    }
    
    // MARK: - 状态变量
    private var didSetupInitialConstraints = false
    private var isViewDismissing = false
    private var isUpdatingLayout = false
    
    // MARK: - 新增 ViewModel
    private let viewModel = LoginViewModel()
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 工厂方法
    private func createBaseTextField(placeholder: String) -> UITextField {
        let textField = UITextField()
        textField.placeholder = placeholder
        textField.font = UIFont.systemFont(ofSize: 15)
        textField.clearButtonMode = .whileEditing
        textField.layer.cornerRadius = 25
        textField.layer.borderWidth = 0.5
        textField.layer.borderColor = UIColor(hex: "#EEEEEE").cgColor
        textField.backgroundColor = .white
        let leftPaddingView = UIView(frame: CGRect(x: 0, y: 0, width: 20, height: DesignSpec.inputFieldHeight))
        textField.leftView = leftPaddingView
        textField.leftViewMode = .always
        textField.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        textField.layer.shadowOffset = CGSize(width: 0, height: 2)
        textField.layer.shadowRadius = 4
        textField.layer.shadowOpacity = 0.1
        
        // 默认禁用自动填充提示
        textField.textContentType = .none
        textField.autocorrectionType = .no
        
        return textField
    }
    
    private func createPhoneTextField() -> UITextField {
        let textField = createBaseTextField(placeholder: "请输入手机号码")
        textField.keyboardType = .phonePad
        textField.delegate = self // 两个手机号框都需要 delegate
        textField.addTarget(self, action: #selector(phoneTextFieldDidChange(_:)), for: .editingChanged)
        
        // 设置为电话号码类型以获得正确的中文自动填充提示
        textField.textContentType = .telephoneNumber
        
        return textField
    }
    
    private func createForgotPasswordButton() -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle("忘记密码？", for: .normal)
        button.setTitleColor(UIColor(hex: "#0256FF"), for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 12)
        button.addTarget(self, action: #selector(forgotPasswordTapped), for: .touchUpInside)
        return button
    }
    
    private func createSwitchButton(title: String, action: Selector) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.setTitleColor(UIColor(hex: "#000000", alpha: 0.45), for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 12)
        button.addTarget(self, action: action, for: .touchUpInside)
        return button
    }
    
    // MARK: - 生命周期方法
    override func viewDidLoad() {
        super.viewDidLoad()
        showNavBar = false
        setupUI()
        setupConstraints()
        setupInputLocalization()
        UIView.performWithoutAnimation {
            view.layoutIfNeeded()
            updateLayout()
            setupScrollViewContentSize()
            view.layoutIfNeeded()
        }
        didSetupInitialConstraints = true
        // 初次加载验证码
        viewModel.refreshCaptcha()
        bindViewModel()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        isViewDismissing = false
        // viewWillAppear 中通常不做复杂的布局更新，确保状态正确即可
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        let tap = UITapGestureRecognizer(target: self, action: #selector(refreshCaptchaTapped))
        captchaImageView_sms.isUserInteractionEnabled = true
        captchaImageView_sms.addGestureRecognizer(tap)
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        // 禁用动画更新渐变层和可能的后期内容尺寸调整 (如旋转)
        UIView.performWithoutAnimation {
            if let gradientLayer = gradientLayer { gradientLayer.frame = view.bounds }
            // 仅在初始布局完成后，才在 viewDidLayoutSubviews 中响应后续布局变化 (例如屏幕旋转)
            // 此时 setupScrollViewContentSize 主要是为了调整 contentSize 和容器 frame
            if didSetupInitialConstraints {
                setupScrollViewContentSize()
            }
            // 不再调用 updateLayout()，避免与 viewDidLoad 中的初始设置冲突
        }
    }
    
    private func setupScrollViewContentSize() {
         if formScrollView.frame.width > 0 {
             let contentWidth = formScrollView.frame.width * 2
             let contentHeight = formScrollView.frame.height
             if formScrollView.contentSize != CGSize(width: contentWidth, height: contentHeight) {
                 formScrollView.contentSize = CGSize(width: contentWidth, height: contentHeight)
             }
             // 使用 frame 设置容器视图位置更可靠，因为此时 scrollview frame 已确定
             smsLoginContainerView.frame = CGRect(x: 0, y: 0, width: formScrollView.frame.width, height: contentHeight)
             passwordLoginContainerView.frame = CGRect(x: formScrollView.frame.width, y: 0, width: formScrollView.frame.width, height: contentHeight)
             // 确保切换后偏移量正确
             let targetOffsetX = (currentLoginMode == .password) ? formScrollView.frame.width : 0
             if formScrollView.contentOffset.x != targetOffsetX {
                  formScrollView.setContentOffset(CGPoint(x: targetOffsetX, y: 0), animated: false) // 布局变化时无动画调整
             }
         }
    }
    
    // MARK: - UI 设置
    private func setupUI() {
        view.backgroundColor = .white
        setupBackground()
        
        // 添加不变的 UI
        view.addSubview(headerContainerView)
        view.addSubview(skipButton)
        headerContainerView.addSubview(logoImageView)
        headerContainerView.addSubview(titleTopImageView)
        headerContainerView.addSubview(titleBottomImageView)
        
        view.addSubview(formScrollView)
        formScrollView.addSubview(smsLoginContainerView)
        formScrollView.addSubview(passwordLoginContainerView)
        
        // 添加短信模式 UI 到对应容器
        smsLoginContainerView.addSubview(phoneTextField_sms)
        smsLoginContainerView.addSubview(captchaTextField_sms)
        smsLoginContainerView.addSubview(captchaImageView_sms)
        smsLoginContainerView.addSubview(codeTextField_sms)
        smsLoginContainerView.addSubview(getCodeButton_sms)
        smsLoginContainerView.addSubview(forgotPasswordButton_sms)
        smsLoginContainerView.addSubview(passwordLoginButton_sms) // 切换按钮
        
        // 添加密码模式 UI 到对应容器
        passwordLoginContainerView.addSubview(phoneTextField_pwd)
        passwordLoginContainerView.addSubview(passwordTextField)
        passwordLoginContainerView.addSubview(forgotPasswordButton_pwd)
        passwordLoginContainerView.addSubview(smsCodeLoginButton) // 切换按钮
        
        // 添加底部不变的 UI
        view.addSubview(loginButton)
        view.addSubview(agreementCheckButton)
        view.addSubview(agreementLabel)
        view.addSubview(leftLine)
        view.addSubview(rightLine)
        view.addSubview(otherLoginLabel)
        view.addSubview(socialLoginStackView)
        socialLoginStackView.addArrangedSubview(wechatButton)
        socialLoginStackView.addArrangedSubview(appleButton)
        
        setupConstraints()
    }
    
    private func setupBackground() {
        gradientLayer = CAGradientLayer()
        gradientLayer!.colors = [ UIColor.white.cgColor, UIColor(red: 1.0, green: 0.95, blue: 0.9, alpha: 0.3).cgColor ]
        gradientLayer!.startPoint = CGPoint(x: 0.5, y: 0)
        gradientLayer!.endPoint = CGPoint(x: 0.5, y: 1)
        gradientLayer!.frame = view.bounds
        view.layer.insertSublayer(gradientLayer!, at: 0)
    }
    
    // MARK: - 约束设置
    private func setupConstraints() {
        // 底部 UI (从下往上)
        socialLoginStackView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(self.view.safeAreaLayoutGuide.snp.bottom)
            make.height.equalTo(64)
        }
        [wechatButton, appleButton].forEach { $0.snp.makeConstraints { make in make.width.height.equalTo(64) } }
        
        otherLoginLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(socialLoginStackView.snp.top).offset(DesignSpec.otherLoginBottomOffset)
        }
        
        leftLine.snp.makeConstraints { make in
            make.right.equalTo(otherLoginLabel.snp.left).offset(-15)
            make.centerY.equalTo(otherLoginLabel)
            make.width.equalTo(50); make.height.equalTo(0.5)
        }
        rightLine.snp.makeConstraints { make in
            make.left.equalTo(otherLoginLabel.snp.right).offset(15)
            make.centerY.equalTo(otherLoginLabel)
            make.width.equalTo(50); make.height.equalTo(0.5)
        }

        loginButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(DesignSpec.horizontalPadding)
            make.right.equalToSuperview().offset(-DesignSpec.horizontalPadding)
            make.bottom.equalTo(otherLoginLabel.snp.top).offset(DesignSpec.loginButtonBottomOffset)
            make.height.equalTo(DesignSpec.inputFieldHeight)
        }
        
        agreementLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview().offset(12)
            make.top.equalTo(loginButton.snp.bottom).offset(DesignSpec.agreementTopOffset)
        }
        agreementCheckButton.snp.makeConstraints { make in
            make.right.equalTo(agreementLabel.snp.left).offset(-8)
            make.centerY.equalTo(agreementLabel)
            make.width.height.equalTo(16)
        }
        
        // ScrollView 固定高度，底部间距动态
        formScrollView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            // Top constraint set relative to header below
            make.height.equalTo(DesignSpec.formContainerHeight) // 固定高度
            // Initial bottom constraint, will be updated in updateLayout
            make.bottom.equalTo(loginButton.snp.top).offset(-DesignSpec.forgotToLoginSpacing)
        }

        // 头部 UI (从上往下)
        skipButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-20)
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(10)
            make.height.equalTo(44)
        }
        
        headerContainerView.snp.makeConstraints { make in
            make.top.equalTo(skipButton.snp.bottom)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(formScrollView.snp.top) // 底部依赖 formScrollView 的顶部
        }
        
        logoImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(24)
            make.width.height.equalTo(DesignSpec.logoSize) // Initial, will be updated
            make.top.equalTo(headerContainerView).offset(DesignSpec.topSpacing) // Initial
        }
        titleTopImageView.snp.makeConstraints { make in
            make.left.equalTo(logoImageView.snp.right).offset(DesignSpec.logoToTitleSpacing)
            make.top.equalTo(logoImageView.snp.top).offset(DesignSpec.topTitleTopOffset)
            make.width.equalTo(DesignSpec.topTitleWidth); make.height.equalTo(DesignSpec.topTitleHeight)
        }
        titleBottomImageView.snp.makeConstraints { make in
            make.top.equalTo(titleTopImageView.snp.bottom).offset(DesignSpec.titleVerticalSpacing)
            make.left.equalTo(logoImageView.snp.right).offset(DesignSpec.logoToBottomTitleOffset)
            make.width.equalTo(DesignSpec.bottomTitleWidth); make.height.equalTo(DesignSpec.bottomTitleHeight)
        }
        
        // --- 短信验证码视图内部约束 ---
        // (相对于 smsLoginContainerView)
        phoneTextField_sms.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview().offset(DesignSpec.horizontalPadding)
            make.right.equalToSuperview().offset(-DesignSpec.horizontalPadding)
            make.height.equalTo(DesignSpec.inputFieldHeight)
        }
        captchaTextField_sms.snp.makeConstraints { make in
            make.top.equalTo(phoneTextField_sms.snp.bottom).offset(DesignSpec.inputFieldSpacing)
            make.left.right.equalTo(phoneTextField_sms)
            make.height.equalTo(DesignSpec.inputFieldHeight)
        }
        captchaImageView_sms.snp.makeConstraints { make in
            make.right.equalTo(captchaTextField_sms).offset(-16)
            make.centerY.equalTo(captchaTextField_sms)
            make.width.equalTo(100); make.height.equalTo(36)
        }
        codeTextField_sms.snp.makeConstraints { make in
            make.top.equalTo(captchaTextField_sms.snp.bottom).offset(DesignSpec.inputFieldSpacing)
            make.left.right.equalTo(phoneTextField_sms) // 恢复原来的宽度
            make.height.equalTo(DesignSpec.inputFieldHeight)
        }
        getCodeButton_sms.snp.makeConstraints { make in
            make.right.equalTo(codeTextField_sms.snp.right).offset(-16) // 使用snp.right确保正确引用
            make.centerY.equalTo(codeTextField_sms)
            make.width.equalTo(100); make.height.equalTo(36)
        }
        forgotPasswordButton_sms.snp.makeConstraints { make in
            make.left.equalTo(codeTextField_sms.snp.left).offset(12)
            make.top.equalTo(codeTextField_sms.snp.bottom).offset(DesignSpec.switchButtonTopOffset)
            make.height.equalTo(DesignSpec.smallButtonHeight)
        }
        passwordLoginButton_sms.snp.makeConstraints { make in
            make.right.equalTo(codeTextField_sms.snp.right).offset(-12)
            make.centerY.equalTo(forgotPasswordButton_sms) // Align vertically with forgot pwd
            make.height.equalTo(DesignSpec.smallButtonHeight)
        }
        
        // --- 密码登录视图内部约束 ---
        // (相对于 passwordLoginContainerView)
        phoneTextField_pwd.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview().offset(DesignSpec.horizontalPadding)
            make.right.equalToSuperview().offset(-DesignSpec.horizontalPadding)
            make.height.equalTo(DesignSpec.inputFieldHeight)
        }
        passwordTextField.snp.makeConstraints { make in
            make.top.equalTo(phoneTextField_pwd.snp.bottom).offset(DesignSpec.inputFieldSpacing)
            make.left.right.equalTo(phoneTextField_pwd)
            make.height.equalTo(DesignSpec.inputFieldHeight)
        }
        forgotPasswordButton_pwd.snp.makeConstraints { make in
            make.left.equalTo(passwordTextField.snp.left).offset(12)
            make.top.equalTo(passwordTextField.snp.bottom).offset(DesignSpec.switchButtonTopOffset)
            make.height.equalTo(DesignSpec.smallButtonHeight)
        }
        smsCodeLoginButton.snp.makeConstraints { make in
            make.right.equalTo(passwordTextField.snp.right).offset(-12)
            make.centerY.equalTo(forgotPasswordButton_pwd) // Align vertically
            make.height.equalTo(DesignSpec.smallButtonHeight)
        }
    }
    
    // MARK: - 绑定 ViewModel
    private func bindViewModel() {
        // 输入框绑定
        phoneTextField_sms.addTarget(self, action: #selector(phoneTextFieldChanged(_:)), for: .editingChanged)
        phoneTextField_pwd.addTarget(self, action: #selector(phoneTextFieldChanged(_:)), for: .editingChanged)
        passwordTextField.addTarget(self, action: #selector(passwordTextFieldChanged), for: .editingChanged)
        codeTextField_sms.addTarget(self, action: #selector(codeTextFieldChanged), for: .editingChanged)
        captchaTextField_sms.addTarget(self, action: #selector(captchaTextFieldChanged), for: .editingChanged)
        agreementCheckButton.addTarget(self, action: #selector(agreementCheckTapped), for: .touchUpInside)

        // 登录模式切换
        smsCodeLoginButton.addTarget(self, action: #selector(switchLoginModeTapped), for: .touchUpInside)
        passwordLoginButton_sms.addTarget(self, action: #selector(switchLoginModeTapped), for: .touchUpInside)

        // 登录按钮
        loginButton.addTarget(self, action: #selector(loginButtonTapped), for: .touchUpInside)
        getCodeButton_sms.addTarget(self, action: #selector(getCodeButtonTapped), for: .touchUpInside)

        // ViewModel -> UI
        viewModel.$isLoginButtonEnabled
            .receive(on: RunLoop.main)
            .sink { [weak self] enabled in
                self?.loginButton.isEnabled = enabled
            }.store(in: &cancellables)
        viewModel.$isLoading
            .receive(on: RunLoop.main)
            .sink { [weak self] loading in
                self?.loginButton.setTitle(loading ? "登录中..." : "登录", for: .normal)
            }.store(in: &cancellables)
        viewModel.$isCodeButtonEnabled
            .receive(on: RunLoop.main)
            .sink { [weak self] enabled in
                self?.getCodeButton_sms.isEnabled = enabled
                self?.getCodeButton_sms.backgroundColor = enabled ? UIColor(hex: "#FFA245") : UIColor(hex: "#CCCCCC")
            }.store(in: &cancellables)
        viewModel.$codeButtonTitle
            .receive(on: RunLoop.main)
            .sink { [weak self] title in
                self?.getCodeButton_sms.setTitle(title, for: .normal)
                self?.getCodeButton_sms.setTitle(title, for: .disabled)
            }.store(in: &cancellables)
        viewModel.$isAgreementChecked
            .receive(on: RunLoop.main)
            .sink { [weak self] checked in
                self?.agreementCheckButton.isSelected = checked
            }.store(in: &cancellables)
        viewModel.$loginMode
            .receive(on: RunLoop.main)
            .sink { [weak self] mode in
                self?.updateLoginModeUI(mode: mode, animated: true)
            }.store(in: &cancellables)
        viewModel.$errorMessage
            .receive(on: RunLoop.main)
            .sink { [weak self] msg in
                if let msg = msg, !msg.isEmpty {
                    self?.showToast(msg)
                    self?.viewModel.resetError()
                }
            }.store(in: &cancellables)
        viewModel.$showAgreementAlert
            .receive(on: RunLoop.main)
            .sink { [weak self] show in
                if show { self?.showAgreementConfirmAlert() }
            }.store(in: &cancellables)
        viewModel.$loginSuccess
            .filter { $0 }
            .receive(on: RunLoop.main)
            .sink { [weak self] _ in
                self?.showToast("登录成功") { [weak self] in self?.dismiss(animated: true) }
            }.store(in: &cancellables)
        viewModel.$showBindPhone
            .compactMap { $0 }
            .receive(on: RunLoop.main)
            .sink { [weak self] bindPhoneId in
                self?.navigateToBindPhone(bindPhoneId: bindPhoneId)
            }.store(in: &cancellables)
        // 验证码图片绑定
        viewModel.$captchaImage
            .receive(on: RunLoop.main)
            .sink { [weak self] image in
                self?.captchaImageView_sms.image = image
            }.store(in: &cancellables)
        viewModel.$isCaptchaLoading
            .receive(on: RunLoop.main)
            .sink { [weak self] loading in
                if loading {
                    let indicator = UIActivityIndicatorView(style: .medium)
                    indicator.center = CGPoint(x: self?.captchaImageView_sms.bounds.midX ?? 0, y: self?.captchaImageView_sms.bounds.midY ?? 0)
                    indicator.startAnimating()
                    indicator.tag = 9999
                    self?.captchaImageView_sms.addSubview(indicator)
                } else {
                    self?.captchaImageView_sms.subviews.filter { $0.tag == 9999 }.forEach { $0.removeFromSuperview() }
                }
            }.store(in: &cancellables)
    }

    // MARK: - 输入框事件
    @objc private func phoneTextFieldChanged(_ textField: UITextField) {
        let text = textField.text ?? ""
        if currentLoginMode == .smsCode {
            viewModel.phone = text
        } else {
            viewModel.phone = text
        }
    }
    @objc private func passwordTextFieldChanged() {
        viewModel.password = passwordTextField.text ?? ""
    }
    @objc private func codeTextFieldChanged() {
        viewModel.code = codeTextField_sms.text ?? ""
    }
    @objc private func captchaTextFieldChanged() {
        viewModel.captcha = captchaTextField_sms.text ?? ""
    }
    @objc private func agreementCheckTapped() {
        viewModel.isAgreementChecked.toggle()
    }
    @objc private func switchLoginModeTapped() {
        let targetMode: LoginMode = (viewModel.loginMode == .smsCode) ? .password : .smsCode
        viewModel.switchLoginMode(targetMode)
    }
    @objc private func loginButtonTapped() {
        view.endEditing(true)
        viewModel.login()
    }
    @objc private func getCodeButtonTapped() {
        view.endEditing(true)
        viewModel.getCode()
    }

    // MARK: - 协议弹窗
    private func showAgreementConfirmAlert() {
        let alertController = UIAlertController(
            title: "温馨提示",
            message: "您需要同意用户协议和隐私协议才能继续",
            preferredStyle: .alert
        )
        let agreeAction = UIAlertAction(title: "同意并继续", style: .default) { [weak self] _ in
            self?.viewModel.agreementConfirmed()
            self?.viewModel.login()
        }
        let cancelAction = UIAlertAction(title: "取消", style: .cancel)
        alertController.addAction(agreeAction)
        alertController.addAction(cancelAction)
        present(alertController, animated: true)
    }

    // MARK: - 事件处理
    @objc private func skipButtonTapped() {
        NSObject.cancelPreviousPerformRequests(withTarget: self)
        isViewDismissing = true
        dismiss(animated: true)
    }
    
    @objc private func wechatLoginButtonTapped() { print("微信登录"); if isWechatInstalled() {
//        loginSuccess()
        self.showToast("微信登录功能即将上线")
    } else { showToast("请先安装微信") } }
    @objc private func appleLoginButtonTapped() {
        print("苹果登录")
        // 检查设备是否支持苹果登录
        if #available(iOS 13.0, *) {
            let appleIDProvider = ASAuthorizationAppleIDProvider()
            let request = appleIDProvider.createRequest()
            request.requestedScopes = [.fullName, .email]

            let authorizationController = ASAuthorizationController(authorizationRequests: [request])
            authorizationController.delegate = self
            authorizationController.presentationContextProvider = self
            authorizationController.performRequests()
        } else {
            showToast("苹果登录需要iOS 13.0及以上版本")
        }
    }
    
    @objc private func refreshCaptchaTapped() {
        viewModel.refreshCaptcha()
    }
    
    @objc private func phoneTextFieldDidChange(_ textField: UITextField) {
        // 移除在手机号变化时刷新验证码的逻辑，因为验证码获取不再依赖手机号
        // 如果需要在手机号变化时进行其他操作，可以保留这个方法
    }
    
    @objc private func forgotPasswordTapped() { 
        print("忘记密码按钮点击") 
        
        // 获取当前输入的手机号（根据当前登录模式选择不同的输入框）
        let phone: String?
        if currentLoginMode == .smsCode {
            phone = phoneTextField_sms.text
        } else { // .password
            phone = phoneTextField_pwd.text
        }
        
        // 创建验证手机号页面
        let vmpnVC = VerifyMobilePhoneNumberViewController()
        vmpnVC.verificationType = .bindPwd
        
        // 如果有手机号，传递给验证页面
        if let phone = phone, isValidPhone(phone) {
            vmpnVC.currentPhoneNumber = phone
        }
        
        // 1. 如果LoginViewController是在导航控制器中，使用push方式
        if let navigationController = self.navigationController {
            navigationController.pushViewController(vmpnVC, animated: true)
            print("使用push方式导航到验证手机号页面")
            return
        }
        
        // 2. 如果是通过present显示的，获取presentingViewController
        guard let presenterToUse = self.presentingViewController else {
            // 如果没有presentingViewController，直接present新页面
            vmpnVC.modalPresentationStyle = .fullScreen
            self.present(vmpnVC, animated: true)
            print("无法获取presentingViewController，直接present验证手机号页面")
            return
        }
        
        // 3. 先关闭当前页面，再由父级呈现新页面
        vmpnVC.modalPresentationStyle = .fullScreen
        self.dismiss(animated: true) {
            presenterToUse.present(vmpnVC, animated: true, completion: nil)
            print("LoginViewController关闭完成，由Presenter弹出验证手机号页面")
        }
    }
    
    @objc private func agreementLabelTapped(_ gesture: UITapGestureRecognizer) {
        let label = gesture.view as! UILabel
        let text = label.attributedText!.string
        let point = gesture.location(in: label)
        if let textPosition = label.characterIndex(at: point) {
            // 调用导航方法
            if (text as NSString).range(of: "《用户协议》").contains(textPosition) {
                navigateToUserAgreement()
                return
            }
            if (text as NSString).range(of: "《隐私协议》").contains(textPosition) {
                navigateToPrivacyPolicy()
                return
            }
        }
    }
    
    // MARK: - 导航方法 (新增占位)
    private func navigateToBindPhone(bindPhoneId: String?) {
        // TODO: 实现跳转到绑定手机页面的逻辑
        let bindVC = PhoneBindingViewController()
        bindVC.bindPhoneId = bindPhoneId ?? ""
//         navigationController?.pushViewController(bindVC, animated: true)
        bindVC.modalPresentationStyle = .fullScreen
         present(bindVC, animated: true)
        print("导航到绑定手机页面，BindPhoneID: \(bindPhoneId ?? "N/A")")
    }

    private func navigateToSetPassword(token: String?, phone: String?) {
        print("准备导航到设置密码页面, Token: \(token ?? "N/A") \(phone ?? "N/A")")
        
        // **修改点 1**: 在 dismiss 之前捕获 presentingViewController
        guard let presenterToUse = self.presentingViewController else {
            print("错误：在调用 dismiss 之前无法获取到 presentingViewController")
            self.dismiss(animated: true) // 至少关闭自己
            return
        }
        
        // 2. 创建 SetNewPasswordViewController 实例
        let setPasswordVC = SetNewPasswordViewController()
        setPasswordVC.phone = phone ?? ""
        setPasswordVC.modalPresentationStyle = .fullScreen
        // setPasswordVC.tempToken = token // 如果需要传递 token
        
        // 3. 先关闭 LoginViewController，在完成后回调中用捕获到的 presenter 弹出
        // **修改点 2**: 闭包不再访问 self 或 self.presentingViewController
        self.dismiss(animated: true) {
            // 使用之前捕获的 presenterToUse
            print("LoginViewController 关闭完成，由 Presenter: \(presenterToUse) 弹出 SetNewPasswordViewController")
            presenterToUse.present(setPasswordVC, animated: true, completion: nil)
        }
    }

    // MARK: - 导航方法 (新增)
    private func navigateToUserAgreement() {
        print("导航到用户协议")
        guard let url = URL(string: "https://gzyoushu.com/privacy/ysh-docuser.htm") else { return }
        let webView = WebViewController(url: url, title: " ")
        print("无法找到 NavigationController，尝试 present WebViewController")
        webView.modalPresentationStyle = .fullScreen // 或者 .automatic
        self.present(webView, animated: true, completion: nil)
    }
    
    private func navigateToPrivacyPolicy() {
        print("导航到隐私政策")
        guard let url = URL(string: "https://gzyoushu.com/privacy/ys-doctset.html") else { return }
        let webView = WebViewController(url: url, title: " ")
        print("无法找到 NavigationController，尝试 present WebViewController")
        webView.modalPresentationStyle = .fullScreen // 或者 .automatic
        self.present(webView, animated: true, completion: nil)
    }
    
    // MARK: - 辅助方法
    private func isValidPhone(_ phone: String) -> Bool {
        let pattern = "^1[3-9]\\d{9}$"
        let predicate = NSPredicate(format: "SELF MATCHES %@", pattern)
        return predicate.evaluate(with: phone)
    }
    
    private func updateGetCodeButtonState(isLoading: Bool) {
        let button = getCodeButton_sms
        button.isEnabled = !isLoading
        button.setTitle(isLoading ? "请求中..." : "获取验证码", for: .disabled)
        button.setTitle("获取验证码", for: .normal)
        button.backgroundColor = isLoading ? UIColor(hex: "#CCCCCC") : UIColor(hex: "#FFA245")
    }
    
    private func startCountdown() {
        stopCountdown() // 确保没有重复的计时器
        
        // 设置结束时间
        let startTime = Date()
        countdownEndTime = startTime.addingTimeInterval(TimeInterval(countdownTotalSeconds))
        
        // 设置按钮初始状态
        let button = getCodeButton_sms
        button.isEnabled = false
        button.backgroundColor = UIColor(hex: "#CCCCCC")
        button.setTitleColor(UIColor(hex: "#FFFFFF"), for: .disabled)
        
        // 使用更精确的倒计时起始值，避免初始延迟
        let initialRemainingSeconds = countdownTotalSeconds
        UIView.performWithoutAnimation {
            button.setTitle("\(initialRemainingSeconds)s后重试", for: .disabled)
            button.layoutIfNeeded() // 强制立即应用更改
        }
        
        // 使用CADisplayLink替代Timer
        let displayLink = CADisplayLink(target: self, selector: #selector(updateCountdown))
        displayLink.preferredFramesPerSecond = 1 // 限制为1帧/秒，减少UI更新频率
        displayLink.add(to: .main, forMode: .common)
        countdownDisplayLink = displayLink
    }
    
    @objc private func updateCountdown() {
        guard let endTime = countdownEndTime else {
            stopCountdown()
            return
        }
        
        let now = Date()
        let timeRemaining = endTime.timeIntervalSince(now)
        
        if timeRemaining <= 0 {
            stopCountdown()
        } else {
            // 计算剩余秒数（向上取整）
            let remainingSeconds = Int(ceil(timeRemaining))
            // 仅当秒数发生变化时才更新UI
            updateCountdownButtonTitle(seconds: remainingSeconds)
        }
    }
    
    private func updateCountdownButtonTitle(seconds: Int? = nil) {
        let remainingSeconds = seconds ?? countdownTotalSeconds
        UIView.performWithoutAnimation {
            getCodeButton_sms.setTitle("\(remainingSeconds)s后重试", for: .disabled)
            getCodeButton_sms.layoutIfNeeded() // 强制立即应用更改
        }
    }
    
    private func stopCountdown() {
        // 停止CADisplayLink
        countdownDisplayLink?.invalidate()
        countdownDisplayLink = nil
        countdownEndTime = nil
        
        // 恢复按钮状态
        let button = getCodeButton_sms
        button.isEnabled = true
        button.backgroundColor = UIColor(hex: "#FFA245")
        
        UIView.performWithoutAnimation {
            button.setTitle("获取验证码", for: .normal)
            button.setTitleColor(.white, for: .normal)
            button.layoutIfNeeded() // 强制立即应用更改
        }
    }
    
    deinit { 
        stopCountdown() 
    }
    
    override func viewWillDisappear(_ animated: Bool) { 
        super.viewWillDisappear(animated)
        isViewDismissing = true
        stopCountdown() 
    }
    
    private func isWechatInstalled() -> Bool { return true }
    private func loginSuccess() { self.showToast("登录成功") { self.dismiss(animated: true) } }
    
    // MARK: - 头部布局更新 & ScrollView 底部间距更新
    private func updateLayout() {
        guard !isViewDismissing && !isUpdatingLayout else { return }
        // 检查必要的视图是否存在
        guard headerContainerView.superview != nil, logoImageView.superview != nil,
              titleTopImageView.superview != nil, titleBottomImageView.superview != nil,
              formScrollView.superview != nil, loginButton.superview != nil
              else {
                print("[DEBUG] Skipping updateLayout - required views not ready")
                return
              }
              
        isUpdatingLayout = true
        
        // 现在 containerHeight 应该基于 viewDidLoad 中第一次 layoutIfNeeded 的结果
        let containerHeight = headerContainerView.frame.height
        guard containerHeight > 0 else {
            isUpdatingLayout = false
            print("[DEBUG] updateLayout: containerHeight is 0, layout likely incomplete.")
            // 正常应该有高度，如果没有，可能是 viewDidLoad 流程问题或约束冲突
            return
        }
        
        let layout = calculateLayout(for: containerHeight)
        let dynamicButtonSpacing = calculateButtonSpacing(containerHeight: containerHeight)
        
        // 在 performWithoutAnimation 块内更新约束，由 viewDidLoad 中的第二次 layoutIfNeeded 应用
        // 更新 Header 内部元素
        logoImageView.snp.updateConstraints { make in make.width.height.equalTo(layout.logoSize); make.top.equalTo(headerContainerView).offset(layout.topSpacing) }
        let scale = layout.scale
        let scaledTitleSpacing = DesignSpec.logoToTitleSpacing * scale
        let scaledTopOffset = DesignSpec.topTitleTopOffset * scale
        let scaledVerticalSpacing = DesignSpec.titleVerticalSpacing * scale
        let scaledBottomOffset = DesignSpec.logoToBottomTitleOffset * scale
        let scaledTopTitleWidth = DesignSpec.topTitleWidth * scale
        let scaledTopTitleHeight = DesignSpec.topTitleHeight * scale
        let scaledBottomTitleWidth = DesignSpec.bottomTitleWidth * scale
        let scaledBottomTitleHeight = DesignSpec.bottomTitleHeight * scale
        titleTopImageView.snp.updateConstraints { make in make.left.equalTo(logoImageView.snp.right).offset(scaledTitleSpacing); make.top.equalTo(logoImageView.snp.top).offset(scaledTopOffset); make.width.equalTo(scaledTopTitleWidth); make.height.equalTo(scaledTopTitleHeight) }
        titleBottomImageView.snp.updateConstraints { make in make.top.equalTo(titleTopImageView.snp.bottom).offset(scaledVerticalSpacing); make.left.equalTo(logoImageView.snp.right).offset(scaledBottomOffset); make.width.equalTo(scaledBottomTitleWidth); make.height.equalTo(scaledBottomTitleHeight) }
        
        // 更新 formScrollView 的底部约束
        formScrollView.snp.updateConstraints { make in
            make.bottom.equalTo(loginButton.snp.top).offset(-dynamicButtonSpacing)
        }
        
        // 移除这里的 layoutIfNeeded
        // view.layoutIfNeeded()
        
        isUpdatingLayout = false
    }
    
    // 恢复 calculateButtonSpacing 函数
    private func calculateButtonSpacing(containerHeight: CGFloat? = nil) -> CGFloat {
        let screenHeight = UIScreen.main.bounds.height
        var finalSpacing: CGFloat = DesignSpec.forgotToLoginSpacing

        if let containerHeight = containerHeight, containerHeight > DesignSpec.totalHeight {
            let extraHeight = containerHeight - DesignSpec.totalHeight
            let extraSpacing = extraHeight * DesignSpec.spacingExpansionRatio
            finalSpacing = min(DesignSpec.maxForgotToLoginSpacing, DesignSpec.forgotToLoginSpacing + extraSpacing)
        } else if screenHeight <= DesignSpec.standardScreenHeight {
            let reductionFactor = max(0.7, screenHeight / DesignSpec.standardScreenHeight)
            finalSpacing = max(DesignSpec.minimumForgotToLoginSpacing, DesignSpec.forgotToLoginSpacing * reductionFactor * DesignSpec.spacingReductionRatio)
        }
        
        // print("[DEBUG] Calculated Button Spacing: \(finalSpacing)") // 可选调试输出
        return finalSpacing
    }
    
    private func calculateLayout(for containerHeight: CGFloat) -> (logoSize: CGFloat, topSpacing: CGFloat, scale: CGFloat) {
        let scale = containerHeight / DesignSpec.totalHeight
        if containerHeight > DesignSpec.totalHeight {
            let logoSize = DesignSpec.logoSize
            let extraSpace = containerHeight - DesignSpec.totalHeight
            let topSpacingRatio: CGFloat = 0.324
            let extraTopSpacing = extraSpace * topSpacingRatio
            let topSpacing = DesignSpec.topSpacing + extraTopSpacing
            return (logoSize, topSpacing, 1.0) // Use 1.0 scale when larger
        } else {
            let logoSize = DesignSpec.logoSize * scale
            let topSpacing = DesignSpec.topSpacing * scale
            return (logoSize, topSpacing, scale)
        }
    }
    
    // 清除验证码输入框
    @objc private func clearCodeTextField() {
        codeTextField_sms.text = ""
        // 触发TextField的editingChanged事件
        codeTextField_sms.sendActions(for: .editingChanged)
    }
    
    // 验证码输入框内容变化时调用
    @objc private func codeTextFieldDidChange() {
        // 更新清除按钮的可见性
        if let rightView = codeTextField_sms.rightView {
            if let clearButton = rightView.subviews.first as? UIButton {
                clearButton.isHidden = (codeTextField_sms.text?.isEmpty ?? true)
            }
        }
    }
    
    // 添加一个辅助方法，检查一个视图控制器是否是设置相关页面
    private func isSettingsRelatedController(_ viewController: UIViewController) -> Bool {
        // 检查是否是设置视图控制器本身
        if viewController is SettingViewController || String(describing: type(of: viewController)).contains("Setting") {
            return true
        }
        
        // 检查是否是设置相关的导航控制器
        if let navController = viewController as? UINavigationController {
            // 检查导航栈中是否包含设置视图控制器
            for vc in navController.viewControllers {
                if vc is SettingViewController || String(describing: type(of: vc)).contains("Setting") {
                    return true
                }
            }
            
            // 检查可见的视图控制器
            if let visibleVC = navController.visibleViewController {
                return isSettingsRelatedController(visibleVC)
            }
        }
        
        // 检查是否是TabBarController，且当前选中的是设置标签
        if let tabBarController = viewController as? UITabBarController,
           let selectedVC = tabBarController.selectedViewController {
            // 检查当前选中的标签页是否是设置相关
            return isSettingsRelatedController(selectedVC)
        }
        
        return false
    }
    
    // 新增方法：设置输入框的本地化
    private func setupInputLocalization() {
        // 尝试通过KVC设置私有属性
        let inputFields: [UITextField] = [
            phoneTextField_sms, 
            phoneTextField_pwd, 
            captchaTextField_sms, 
            codeTextField_sms, 
            passwordTextField
        ]
        
        for textField in inputFields {
            if let textField = textField as? NSObject {
                if textField.responds(to: NSSelectorFromString("setPreferredLanguages:")) {
                    textField.setValue(["zh-Hans"], forKey: "preferredLanguages")
                }
                if textField.responds(to: NSSelectorFromString("setTextInputSource:")) {
                    textField.setValue("zh-Hans", forKey: "textInputSource")
                }
                if textField.responds(to: NSSelectorFromString("setTextInputMode:")) {
                    let mode = NSObject()
                    if let mode = mode as? NSObject, mode.responds(to: NSSelectorFromString("setPrimaryLanguage:")) {
                        mode.setValue("zh-Hans", forKey: "primaryLanguage")
                        textField.setValue(mode, forKey: "textInputMode")
                    }
                }
            }
        }
    }

    // 新增：UI层滚动动画方法，替代原switchToMode
    private func updateLoginModeUI(mode: LoginMode, animated: Bool) {
        // 清空输入框内容
        [phoneTextField_sms, captchaTextField_sms, codeTextField_sms, phoneTextField_pwd, passwordTextField].forEach { $0.text = "" }
        // 滚动到目标视图
        let targetOffsetX = (mode == .password) ? formScrollView.frame.width : 0
        formScrollView.setContentOffset(CGPoint(x: targetOffsetX, y: 0), animated: animated)
    }
}

// MARK: - UITextFieldDelegate
extension LoginViewController: UITextFieldDelegate {
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
         if textField == phoneTextField_sms || textField == phoneTextField_pwd {
            let currentText = textField.text ?? ""
            let newLength = currentText.count + string.count - range.length
            return newLength <= 11
        } else if textField == codeTextField_sms {
            // 限制验证码长度为6位数字
            let currentText = textField.text ?? ""
            let newLength = currentText.count + string.count - range.length

            // 只允许输入数字
            let allowedCharacters = CharacterSet.decimalDigits
            let characterSet = CharacterSet(charactersIn: string)
            let isNumber = allowedCharacters.isSuperset(of: characterSet)

            return newLength <= 6 && isNumber
        }
        return true
    }
}

// MARK: - 苹果登录代理
@available(iOS 13.0, *)
extension LoginViewController: ASAuthorizationControllerDelegate, ASAuthorizationControllerPresentationContextProviding {

    // 苹果登录成功
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
            let userIdentifier = appleIDCredential.user
            let fullName = appleIDCredential.fullName
            let email = appleIDCredential.email
            let authorizationCode = appleIDCredential.authorizationCode
            let identityToken = appleIDCredential.identityToken

            // 打印获取到的信息
            print("=== 苹果登录成功 ===")
            print("User ID: \(userIdentifier)")
            print("Full Name: \(fullName?.formatted() ?? "未提供")")
            print("Email: \(email ?? "未提供")")

            // 处理 authorizationCode
            if let authCode = authorizationCode {
                let authCodeString = String(data: authCode, encoding: .utf8) ?? ""
                print("Authorization Code: \(authCodeString)")
            }

            // 处理 identityToken
            if let idToken = identityToken {
                let idTokenString = String(data: idToken, encoding: .utf8) ?? ""
                print("Identity Token: \(idTokenString)")

                // 调用ViewModel的苹果登录方法，将identityToken作为accessToken传递
                DispatchQueue.main.async {
                    self.viewModel.loginWithApple(accessToken: idTokenString)
                }
            }

            print("=== 苹果登录信息结束 ===")
        }
    }

    // 苹果登录失败
    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        print("苹果登录失败: \(error.localizedDescription)")

        // 处理不同类型的错误
        if let authError = error as? ASAuthorizationError {
            switch authError.code {
            case .canceled:
                print("用户取消了苹果登录")
                // 用户取消，不显示错误提示
                break
            case .failed:
                showToast("苹果登录失败，请重试")
            case .invalidResponse:
                showToast("苹果登录响应无效")
            case .notHandled:
                showToast("苹果登录未处理")
            case .unknown:
                showToast("苹果登录遇到未知错误")
            @unknown default:
                showToast("苹果登录遇到未知错误")
            }
        } else {
            showToast("苹果登录失败: \(error.localizedDescription)")
        }
    }

    // 提供展示上下文
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        return self.view.window!
    }
}
