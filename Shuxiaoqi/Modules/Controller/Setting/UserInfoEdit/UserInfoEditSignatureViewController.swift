//
//  UserInfoEditSignatureViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/5/19.
//
/*
[ ] 输入"@"弹出用户搜索
[ ] 实时根据输入内容搜索用户
[ ] 选中用户后插入高亮用户名
[ ] 支持多次艾特
[ ] 高亮用户名和"@"符号
[ ] 实际上传内容为 @用户id\
[ ] 删除高亮用户名时同步删除id
[ ] 支持辅助功能
*/

import UIKit
import IQTextView
import Kingfisher

class UserInfoEditSignatureViewController: BaseViewController, UITextViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    // MARK: - 属性
    var currentSignature: String = ""   // 外部传入
    var maxLength: Int = 100
    var onSignatureUpdated: ((String, [String: String]) -> Void)?
    var remainingEdits: Int = 0 // 剩余编辑次数
    var endDate: String = "" // 过期时间

    // 记录初始值
    private var initialSignature: String = ""

    // MARK: - UI组件
    private let signatureTextView = IQTextView()
    private let countLabel = UILabel()
    private let tipLabel = UILabel()
    private let separatorLine = UIView()
    // 新增：卡片视图及高度约束
    private let cardView = UIView()
    private var cardHeightConstraint: NSLayoutConstraint!

    // MARK: - @艾特功能相关结构体和属性
    struct MentionUser {
        let id: String
        let name: String
        var range: NSRange
    }

    private var mentionUsers: [MentionUser] = []
    private var isMentioning: Bool = false
    private var mentionStartLocation: Int = 0
    private var mentionKeyword: String = ""
    private var mentionPage: Int = 0
    private var mentionPageSize: Int = 10
    private var mentionHasMore: Bool = true
    private var mentionUsersData: [UserSearchResultsItem] = []
    private var mentionTotal: Int = 0 // 新增总数记录

    private var mentionCollectionView: UICollectionView!
    var mentionedUserDict: [String: String] = [:] // 新增属性

    override func viewDidLoad() {
        super.viewDidLoad()
        navTitle = "编辑简介"
        navBar.backgroundColor = UIColor(hex: "#F5F5F5")
        showBackButton = true
        rightNavTitle = "确认"
        rightNavAction = #selector(confirmButtonTapped)
        rightNavButtonTintColor = UIColor(hex: "#FF6236")
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        initialSignature = currentSignature
        setupUI()
        updateCountLabel()
        updateConfirmButtonState()
        setupMentionCollectionView()
    }

    private func setupUI() {
        // 白色圆角卡片
        cardView.backgroundColor = .white
        cardView.layer.cornerRadius = 5
        cardView.layer.masksToBounds = true
        cardView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(cardView)
        cardHeightConstraint = cardView.heightAnchor.constraint(equalToConstant: 120)
        NSLayoutConstraint.activate([
            cardView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 25),
            cardView.leftAnchor.constraint(equalTo: contentView.leftAnchor, constant: 20),
            cardView.rightAnchor.constraint(equalTo: contentView.rightAnchor, constant: -20),
            cardHeightConstraint
        ])

        // 多行输入框
        signatureTextView.font = UIFont.systemFont(ofSize: 14)
        signatureTextView.textColor = UIColor(hex: "#333333")
        signatureTextView.backgroundColor = .clear
        signatureTextView.delegate = self
        if !currentSignature.isEmpty && !mentionedUserDict.isEmpty {
            signatureTextView.attributedText = UserInformationEditingPage.makeHighlightedSignature(from: currentSignature, mentionedUser: mentionedUserDict)
        } else {
        signatureTextView.text = currentSignature
        }
        signatureTextView.placeholder = "介绍一下自己"
        signatureTextView.textContainerInset = UIEdgeInsets(top: 12, left: 12, bottom: 28, right: 12)
        signatureTextView.translatesAutoresizingMaskIntoConstraints = false
        cardView.addSubview(signatureTextView)
        NSLayoutConstraint.activate([
            signatureTextView.topAnchor.constraint(equalTo: cardView.topAnchor),
            signatureTextView.leftAnchor.constraint(equalTo: cardView.leftAnchor),
            signatureTextView.rightAnchor.constraint(equalTo: cardView.rightAnchor),
            signatureTextView.heightAnchor.constraint(equalToConstant: 120)
        ])

        // 分割线
        separatorLine.backgroundColor = UIColor(hex: "#EEEEEE")
        separatorLine.translatesAutoresizingMaskIntoConstraints = false
        cardView.addSubview(separatorLine)
        NSLayoutConstraint.activate([
            separatorLine.topAnchor.constraint(equalTo: signatureTextView.bottomAnchor),
            separatorLine.leftAnchor.constraint(equalTo: cardView.leftAnchor),
            separatorLine.rightAnchor.constraint(equalTo: cardView.rightAnchor),
            separatorLine.heightAnchor.constraint(equalToConstant: 1)
        ])

        // 字数统计
        countLabel.font = UIFont.systemFont(ofSize: 14)
        countLabel.textColor = UIColor(hex: "#BBBBBB")
        countLabel.textAlignment = .right
        countLabel.translatesAutoresizingMaskIntoConstraints = false
        cardView.addSubview(countLabel)
        NSLayoutConstraint.activate([
            countLabel.rightAnchor.constraint(equalTo: cardView.rightAnchor, constant: -12),
            countLabel.bottomAnchor.constraint(equalTo: signatureTextView.bottomAnchor, constant: -8)
        ])

        // 提示文本
        tipLabel.font = UIFont.systemFont(ofSize: 12)
        tipLabel.textColor = UIColor(hex: "#777777")
        
        // 根据剩余编辑次数更新提示文本
        if remainingEdits > 0 {
            if !endDate.isEmpty {
                tipLabel.text = "7天内可修改\(remainingEdits)次简介，\(endDate)之前还可修改\(remainingEdits)次。"
            } else {
                tipLabel.text = "7天内可修改\(remainingEdits)次简介。"
            }
        } else {
            if !endDate.isEmpty {
                tipLabel.text = "您的简介编辑次数已用完，\(endDate)后可重新编辑。"
            } else {
                tipLabel.text = "您的简介编辑次数已用完。"
            }
        }
        
        tipLabel.numberOfLines = 0
        tipLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(tipLabel)
        NSLayoutConstraint.activate([
            tipLabel.topAnchor.constraint(equalTo: cardView.bottomAnchor, constant: 24),
            tipLabel.leftAnchor.constraint(equalTo: contentView.leftAnchor, constant: 32),
            tipLabel.rightAnchor.constraint(equalTo: contentView.rightAnchor, constant: -32)
        ])
    }

    private func setupMentionCollectionView() {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 12
        layout.sectionInset = UIEdgeInsets(top: 0, left: 8, bottom: 0, right: 8)
        mentionCollectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        mentionCollectionView.backgroundColor = .white
        mentionCollectionView.showsHorizontalScrollIndicator = false
        mentionCollectionView.dataSource = self
        mentionCollectionView.delegate = self
        mentionCollectionView.isHidden = true
        mentionCollectionView.translatesAutoresizingMaskIntoConstraints = false
        mentionCollectionView.register(MentionUserCell.self, forCellWithReuseIdentifier: "MentionUserCell")
        cardView.addSubview(mentionCollectionView)
        NSLayoutConstraint.activate([
            mentionCollectionView.topAnchor.constraint(equalTo: separatorLine.bottomAnchor, constant: 0),
            mentionCollectionView.leftAnchor.constraint(equalTo: cardView.leftAnchor),
            mentionCollectionView.rightAnchor.constraint(equalTo: cardView.rightAnchor),
            mentionCollectionView.heightAnchor.constraint(equalToConstant: 98)
        ])
    }

    // MARK: - 逻辑
    private func updateCountLabel() {
        let count = signatureTextView.text.count
        countLabel.text = "\(count)/\(maxLength)"
    }

    private func hasChanged() -> Bool {
        return signatureTextView.text != initialSignature
    }

    private func updateConfirmButtonState() {
        let changed = hasChanged()
        rightNavButton?.isEnabled = changed
        rightNavButton?.setTitleColor(UIColor(hex: "#FF6236"), for: .normal)
        rightNavButton?.alpha = changed ? 1.0 : 0.5
    }

    // MARK: - 同步高亮@用户名的range
    private func syncMentionRanges() {
        guard let attr = signatureTextView.attributedText else { return }
        var newMentions: [MentionUser] = []
        attr.enumerateAttribute(NSAttributedString.Key("mentionId"), in: NSRange(location: 0, length: attr.length), options: []) { value, range, _ in
            if let id = value as? String, range.length > 0 {
                // 去掉开头的@和尾部可能存在的零宽空格
                let raw = (attr.string as NSString).substring(with: range)
                let trimmed = raw.trimmingCharacters(in: CharacterSet(charactersIn: "@\u{200B}"))
                newMentions.append(MentionUser(id: id, name: trimmed, range: range))
            }
        }
        mentionUsers = newMentions
    }

    // MARK: - UITextViewDelegate
    func textViewDidChange(_ textView: UITextView) {
        // 限制最大长度
        if textView.text.count > maxLength {
            textView.text = String(textView.text.prefix(maxLength))
        }
        updateCountLabel()
        updateConfirmButtonState()
        handleMentionInput(textView)
        syncMentionRanges()
    }

    private func handleMentionInput(_ textView: UITextView) {
        let cursorPosition = textView.selectedRange.location
        let text = textView.text ?? ""

        // 若当前处于@输入模式，检查起始位置的字符是否仍为"@"，
        // 若已被删除则立即结束@模式并隐藏选择器
        if isMentioning {
            let nsText = text as NSString
            if mentionStartLocation >= nsText.length || nsText.character(at: mentionStartLocation) != 64 { // 64 -> "@"
                isMentioning = false
                showMentionCollectionView(false)
                return
            }
        }
        // 检测@输入
        if isMentioning {
            mentionPage = 0 // 只要关键字变化就重置分页
            // 获取@后内容
            let mentionRange = NSRange(location: mentionStartLocation, length: cursorPosition - mentionStartLocation)
            if let range = Range(mentionRange, in: text) {
                mentionKeyword = String(text[range]).replacingOccurrences(of: "@", with: "")
                searchMentionUsers(keyword: mentionKeyword, page: 0)
            }
        } else if let lastChar = text.prefix(cursorPosition).last, lastChar == "@" {
            isMentioning = true
            mentionStartLocation = cursorPosition - 1
            mentionKeyword = ""
            mentionPage = 0 // 新@输入时重置分页
            searchMentionUsers(keyword: "", page: 0)
            showMentionCollectionView(true)
        } else {
            showMentionCollectionView(false)
            isMentioning = false
        }
    }

    private func showMentionCollectionView(_ show: Bool) {
        mentionCollectionView.isHidden = !show
        cardHeightConstraint.constant = show ? 219 : 120
        UIView.animate(withDuration: 0.25) {
            self.view.layoutIfNeeded()
        }
    }

    private func searchMentionUsers(keyword: String, page: Int) {
        APIManager.shared.searchUser(keywords: keyword, page: page, size: mentionPageSize, isIgnore: true) { [weak self] result in
            switch result {
            case .success(let data):
                let newList = data.data.list
                self?.mentionTotal = data.data.total

                // 如果开启了isIgnore且搜索不到用户，关闭@功能
                if page == 0 && newList.isEmpty && !keyword.isEmpty {
                    DispatchQueue.main.async {
                        self?.showMentionCollectionView(false)
                        self?.isMentioning = false
                    }
                    return
                }

                if page == 0 {
                    self?.mentionUsersData = newList
                } else if !newList.isEmpty {
                    self?.mentionUsersData.append(contentsOf: newList)
                }
                // 只有还有数据且本次返回有数据才允许继续翻页
                self?.mentionHasMore = (self?.mentionUsersData.count ?? 0) < (self?.mentionTotal ?? 0) && !newList.isEmpty
                self?.mentionCollectionView.reloadData()
            case .failure:
                if page == 0 {
                    self?.mentionUsersData = []
                }
                self?.mentionHasMore = false
                self?.mentionCollectionView.reloadData()
                // API失败时也关闭@功能
                if !keyword.isEmpty {
                    DispatchQueue.main.async {
                        self?.showMentionCollectionView(false)
                        self?.isMentioning = false
                    }
                }
            }
        }
    }

    // MARK: - UICollectionViewDataSource & Delegate
    @objc func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return mentionUsersData.count
    }
    @objc func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "MentionUserCell", for: indexPath) as! MentionUserCell
        let user = mentionUsersData[indexPath.item]
        cell.configure(with: user)
        print("cellForItemAt called, index: \(indexPath.item)")
        return cell
    }
    @objc func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let user = mentionUsersData[indexPath.item]
        insertMentionUser(user)
        showMentionCollectionView(false)
        isMentioning = false
    }
    @objc func scrollViewDidScroll(_ scrollView: UIScrollView) {
        let offsetX = scrollView.contentOffset.x
        let contentWidth = scrollView.contentSize.width
        let width = scrollView.frame.width
        // 只有未加载完所有数据时才允许翻页
        if mentionHasMore && offsetX + width > contentWidth - 40 {
            // 判断是否还有更多页
            if mentionUsersData.count < mentionTotal {
                mentionPage += 1
                searchMentionUsers(keyword: mentionKeyword, page: mentionPage)
            } else {
                mentionHasMore = false
            }
        }
    }
    @objc func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 74, height: 70)
    }

    // MARK: - 插入"@"用户
    private func insertMentionUser(_ user: UserSearchResultsItem) {
        guard let textView = signatureTextView as? UITextView else { return }
        let text = textView.text ?? ""
        let cursorPosition = textView.selectedRange.location
        let mentionRange = NSRange(location: mentionStartLocation, length: cursorPosition - mentionStartLocation)
        
        // 使用 textStorage 直接修改，保证 RLE 数组一致性
        let textStorage = textView.textStorage
        textStorage.beginEditing()
        
        // 创建@用户名的富文本，设置高亮
        let mentionDisplay = "@" + user.displayNickName
        let mentionAttr = NSMutableAttributedString(string: mentionDisplay)
        mentionAttr.addAttribute(.foregroundColor, value: UIColor.systemBlue, range: NSRange(location: 0, length: mentionDisplay.count))
        mentionAttr.addAttribute(NSAttributedString.Key("mentionId"), value: user.customerId, range: NSRange(location: 0, length: mentionDisplay.count))
        
        // 创建零宽空格的富文本，使用默认文本属性
        let separatorAttr = NSMutableAttributedString(string: "\u{200B}")
        separatorAttr.addAttribute(.font, value: UIFont.systemFont(ofSize: 14), range: NSRange(location: 0, length: 1))
        separatorAttr.addAttribute(.foregroundColor, value: UIColor(hex: "#333333"), range: NSRange(location: 0, length: 1))
        
        // 先替换@用户名
        textStorage.replaceCharacters(in: mentionRange, with: mentionAttr)
        
        // 然后在@用户名后插入零宽空格（带默认属性）
        let insertPosition = mentionRange.location + mentionDisplay.count
        textStorage.insert(separatorAttr, at: insertPosition)
        
        textStorage.endEditing()
        
        // 将光标移动到零宽空格之后
        let newCursor = insertPosition + 1
        textView.selectedRange = NSRange(location: newCursor, length: 0)
        
        mentionedUserDict[user.customerId] = user.nickName // 同步更新字典
        isMentioning = false
        mentionKeyword = ""
        mentionStartLocation = 0
        syncMentionRanges() // 插入后立即同步range
    }

    // MARK: - 富文本删除"@"用户名整体
    func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
        syncMentionRanges()
        // 禁止在高亮@用户名内部插入或删除内容，只有整体选中时才允许替换
        for (index, mention) in mentionUsers.enumerated() {
            if NSIntersectionRange(range, mention.range).length > 0 {
                // 如果是删除操作，整体删除高亮块
                if text.isEmpty {
                    let storage = textView.textStorage
                    storage.beginEditing()
                    storage.replaceCharacters(in: mention.range, with: "")
                    storage.endEditing()
                    textView.selectedRange = NSRange(location: mention.range.location, length: 0)
                    mentionedUserDict.removeValue(forKey: mention.id)
                    mentionUsers.remove(at: index)
                    return false
                } else {
                    // 如果是插入内容，整体替换高亮块为新内容
                    let storage = textView.textStorage
                    storage.beginEditing()
                    storage.replaceCharacters(in: mention.range, with: text)
                    storage.endEditing()
                    textView.selectedRange = NSRange(location: mention.range.location + (text as NSString).length, length: 0)
                    mentionedUserDict.removeValue(forKey: mention.id)
                    mentionUsers.remove(at: index)
                    return false
                }
            }
        }
        // 禁止在高亮块内部插入内容（即光标在高亮块内但未选中整个高亮块）
        for mention in mentionUsers {
            if NSLocationInRange(range.location, mention.range) {
                return false
            }
        }
        // 限制最大长度和行数
        if let str = textView.text, let textRange = Range(range, in: str) {
            let newText = str.replacingCharacters(in: textRange, with: text)
            let lines = newText.components(separatedBy: "\n")
            if lines.count > 8 { return false }
            if newText.count > maxLength { return false }
        }
        return true
    }

    // MARK: - 确认按钮
    @objc private func confirmButtonTapped() {
        guard hasChanged() else { return }
        // 直接处理纯文本，避免任何富文本操作
        let plainText = signatureTextView.text ?? ""
        var processedText = plainText
        
        // 1. 移除所有零宽空格
        processedText = processedText.replacingOccurrences(of: "\u{200B}", with: "")
        
        // 2. 替换所有 @用户名 为 @id\
        // 为避免部分用户名是其他用户名的子串，按长度降序排序
        let sortedUsers = mentionedUserDict.sorted { $0.value.count > $1.value.count }
        
        for (userId, userName) in sortedUsers {
            let pattern = "@" + userName
            let replacement = "@" + userId + "\\"
            processedText = processedText.replacingOccurrences(of: pattern, with: replacement)
        }
        
        // 3. 回调处理后的文本和用户字典
        onSignatureUpdated?(processedText, mentionedUserDict)
        navigationController?.popViewController(animated: true)
    }

    // MARK: - 用户选择器Cell
    class MentionUserCell: UICollectionViewCell {
        private let avatarImageView = UIImageView()
        private let nameLabel = UILabel()
        override init(frame: CGRect) {
            super.init(frame: frame)
            avatarImageView.layer.cornerRadius = 24
            avatarImageView.clipsToBounds = true
            avatarImageView.translatesAutoresizingMaskIntoConstraints = false
            nameLabel.font = UIFont.systemFont(ofSize: 13)
            nameLabel.textAlignment = .center
            nameLabel.translatesAutoresizingMaskIntoConstraints = false
            contentView.addSubview(avatarImageView)
            contentView.addSubview(nameLabel)
            NSLayoutConstraint.activate([
                avatarImageView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 4),
                avatarImageView.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
                avatarImageView.widthAnchor.constraint(equalToConstant: 48),
                avatarImageView.heightAnchor.constraint(equalToConstant: 48),
                nameLabel.topAnchor.constraint(equalTo: avatarImageView.bottomAnchor, constant: 4),
                nameLabel.leftAnchor.constraint(equalTo: contentView.leftAnchor, constant: 0),
                nameLabel.rightAnchor.constraint(equalTo: contentView.rightAnchor, constant: 0),
                nameLabel.heightAnchor.constraint(equalToConstant: 20)
            ])
        }
        required init?(coder: NSCoder) { super.init(coder: coder) }
        func configure(with user: UserSearchResultsItem) {
            nameLabel.text = user.displayNickName
            if let url = URL(string: user.wxAvator), !user.wxAvator.isEmpty {
                avatarImageView.kf.setImage(with: url, placeholder: UIImage(named: "avatar_placeholder"))
            } else {
                avatarImageView.image = UIImage(named: "avatar_placeholder")
            }
        }
    }
}
